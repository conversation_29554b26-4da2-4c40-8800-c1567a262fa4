<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n-key="title">24小时时间管理</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
            justify-content: center;
        }

        .app-container {
            display: flex;
            max-width: 1100px;
            /* Increased width */
            width: 100%;
        }

        .left-panel {
            width: 70px;
            display: flex;
            flex-direction: column;
            margin-right: 10px;
            position: sticky;
            /* Default sticky for desktop */
            top: 10px;
            height: fit-content;
            z-index: 50;
            /* Ensure it's above other content when sticky */
        }

        .color-panel,
        .settings-panel,
        .storage-panel {
            padding: 10px 5px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 10px;
        }

        .completion-toggle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e0e0e0;
            margin: 5px auto;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            border: 2px solid transparent;
        }

        .completion-toggle.completed {
            background-color: #80a88a;
            border-color: #c5cac5;
        }

        .completion-toggle::after {
            content: "✓";
            color: white;
            font-size: 20px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .completion-toggle.completed::after {
            opacity: 1;
        }

        .main-content {
            flex: 1;
            max-width: 650px;
            /* Max width for the planner part */
        }

        h1 {
            font-size: 18px;
            text-align: center;
            color: #333;
            margin: 0 0 10px 0;
        }

        .date-selector {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 14px;
            /* This whole div will be hidden */
        }

        /* .date-nav is inside .date-selector, no need to hide explicitly if parent is hidden */
        #current-date {
            font-size: 12px;
            padding: 2px;
            text-align: center;
        }

        .hour-row {
            display: flex;
            margin-bottom: 3px;
            height: 30px;
            font-size: 12px;
            position: relative;
        }

        .hour-label {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            background-color: #e0e0e0;
            border-radius: 3px 0 0 3px;
            transition: all 0.2s;
        }

        .hour-label.completed {
            opacity: 0.6;
        }

        .color-block {
            width: 50px;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.2s;
            background-color: #ffffff;
        }

        .color-block.completed {
            opacity: 0.6;
        }

        .task-input {
            flex: 1;
            padding: 0 5px;
            border: 1px solid #ddd;
            border-left: none;
            font-size: 12px;
            min-width: 0;
            transition: all 0.2s;
        }

        .task-input.completed {
            opacity: 0.6;
            background-color: #f5f5f5;
        }

        .task-actions {
            display: flex;
            width: 80px;
            position: relative;
            z-index: 1;
        }

        .complete-checkbox {
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
        }

        .action-btn {
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 0 3px 3px 0;
            cursor: pointer;
            font-size: 12px;
            position: relative;
            z-index: 2;
        }

        .action-menu {
            position: absolute;
            right: 0;
            top: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            z-index: 10;
            display: none;
        }

        .action-menu button {
            display: block;
            width: 100%;
            padding: 5px 10px;
            text-align: left;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 12px;
            white-space: nowrap;
        }

        .action-menu button:hover {
            background-color: #f0f0f0;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            gap: 5px;
        }

        .controls button {
            padding: 5px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
            margin: 5px auto;
            transition: transform 0.2s;
            position: relative;
        }

        .color-option.selected {
            border-color: #333;
            transform: scale(1.1);
        }

        .color-option-title {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 10px;
            white-space: nowrap;
            display: none;
            z-index: 100;
        }

        .color-option:hover .color-option-title {
            display: block;
        }

        .save-btn {
            display: block;
            width: 100%;
            padding: 8px;
            background-color: #73665d;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }

        .save-btn.hidden {
            display: none;
        }

        .app-info {
            text-align: center;
            color: #666;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .settings-btn {
            width: 100%;
            padding: 5px;
            font-size: 12px;
            border-radius: 3px;
            border: none;
            cursor: pointer;
            background-color: #5d5d5d;
            color: white;
            margin-bottom: 5px;
        }

        #storage-btn {
            background-color: #795548;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            width: 350px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-title {
            margin-top: 0;
            text-align: center;
        }

        .time-selector {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
        }

        .time-selector select {
            padding: 5px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
        }

        .modal-buttons button {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .confirm-btn {
            background-color: #c09d84;
            color: white;
        }

        .cancel-btn {
            background-color: #9d4e49;
            color: white;
        }

        .hidden-row {
            display: none;
        }

        .sleep-divider {
            height: 2px;
            background-color: #5d5d5d;
            margin: 5px 0;
            position: relative;
        }

        .sleep-divider::after {
            content: "睡眠时间";
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: #f5f5f5;
            padding: 0 5px;
            font-size: 10px;
            color: #73665d;
        }

        .storage-category {
            margin: 15px 0 5px 0;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 3px;
        }

        .storage-item {
            padding: 8px;
            margin: 5px 0;
            background-color: #f9f9f9;
            border-radius: 3px;
            border: 1px solid #ddd;
            cursor: pointer;
            position: relative;
            padding-left: 30px;
        }

        .storage-item:hover {
            background-color: #f0f0f0;
        }

        .storage-item-time {
            font-weight: bold;
            margin-bottom: 3px;
        }

        .storage-item-task {
            font-size: 12px;
            color: #666;
        }

        .storage-item-completed {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
        }

        .storage-item-actions {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 5px;
        }

        .storage-item-actions button {
            padding: 2px 5px;
            font-size: 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .edit-item {
            background-color: #FFC107;
            color: black;
        }

        .delete-item {
            background-color: #f44336;
            color: white;
        }

        .settings-section {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .settings-section h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }

        .settings-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .settings-option label {
            font-size: 14px;
        }

        .settings-option select {
            padding: 3px;
            width: 100px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #2196F3;
        }

        input:checked+.slider:before {
            transform: translateX(26px);
        }

        .category-switch {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .category-switch button {
            flex: 1;
            padding: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .category-followup {
            background-color: #2196F3;
            color: white;
        }

        .category-followup:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .category-todo {
            background-color: #73665d;
            color: white;
        }

        .category-todo:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .color-edit-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 10px;
        }

        .color-edit-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .color-edit-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1px solid #ddd;
        }

        .color-edit-input {
            flex: 1;
            padding: 3px;
            font-size: 12px;
        }

        .notes-panel {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            padding: 10px;
            position: relative;
            display: none;
        }

        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .notes-title {
            font-weight: bold;
            font-size: 14px;
        }

        .notes-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .notes-select {
            padding: 3px 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
            font-size: 12px;
        }

        .notes-content {
            min-height: 100px;
            border: 1px solid #eee;
            padding: 8px;
            font-size: 13px;
        }

        .current-hour {
            font-weight: bold;
            background-color: #333;
            color: white;
        }

        /* Right Panel (Calendar) */
        .right-panel {
            width: 280px;
            margin-left: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            height: fit-content;
            position: sticky;
            top: 10px;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
            /* For collapse animation */
            overflow: hidden;
            /* Important for collapse animation */
        }

        .right-panel.collapsed .calendar-grid,
        .right-panel.collapsed .score-display,
        .right-panel.collapsed #prev-calendar-month,
        /* Hide nav buttons when collapsed */
        .right-panel.collapsed #next-calendar-month,
        .right-panel.collapsed #current-calendar-month-year {
            display: none;
        }

        .right-panel.collapsed {
            padding-top: 5px;
            /* Adjust padding when collapsed */
            padding-bottom: 5px;
            max-height: 40px;
            /* Adjust to fit only the collapse button or a small title */
        }

        .calendar-panel-header {
            /* New wrapper for title and collapse button */
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        #calendar-panel-title {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
        }

        #toggle-calendar-collapse {
            background: none;
            border: 1px solid #ddd;
            cursor: pointer;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        #current-calendar-month-year {
            font-size: 16px;
            margin: 0;
            font-weight: bold;
        }

        .calendar-header button {
            background: none;
            border: 1px solid #ddd;
            cursor: pointer;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 14px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            text-align: center;
        }

        .calendar-day-header {
            font-weight: bold;
            font-size: 12px;
            padding: 5px 0;
            background-color: #f0f0f0;
            border-radius: 2px;
        }

        .calendar-day {
            font-size: 12px;
            padding: 5px 2px;
            min-height: 35px;
            border: 1px solid #eee;
            position: relative;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
        }

        .calendar-day:hover {
            background-color: #f9f9f9;
        }

        .calendar-day.empty {
            background-color: #fdfdfd;
            color: #ccc;
            cursor: default;
            pointer-events: none;
        }

        .calendar-day.today {
            background-color: #fffbe6;
            font-weight: bold;
            border: 1px solid #ffe58f;
        }

        .calendar-day.selected-planner-date {
            outline: 2px solid #c09d84;
            outline-offset: -2px;
            background-color: #f5efea;
        }

        .day-score-marker {
            margin-top: 2px;
            font-size: 9px;
            color: green;
            font-weight: bold;
        }

        .score-display {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }

        #total-score {
            color: #c09d84;
            font-size: 16px;
        }

        /* Media query adjustments */
        @media (max-width: 1024px) {
            .app-container {
                flex-direction: column;
                max-width: 100%;
            }

            .right-panel {
                width: auto;
                max-width: 400px;
                margin-left: auto;
                /* Centering */
                margin-right: auto;
                /* Centering */
                margin-top: 10px;
                position: static;
                /* No sticky for right panel when stacked */
            }

            .main-content {
                max-width: 100%;
            }

            .left-panel {
                /* For mobile, make it sticky at the top */
                position: sticky;
                top: 0;
                /* Stick to the very top of viewport */
                background-color: #f5f5f5;
                /* Match body background to avoid transparency issues */
                padding-bottom: 5px;
                /* Add some padding if elements are too close to bottom */
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                /* Optional: add shadow when sticky */
            }
        }

        @media (max-width: 767px) {
            .app-container {
                flex-direction: column;
            }

            .left-panel {
                width: 100%;
                flex-direction: row;
                justify-content: space-between;
                margin-right: 0;
                margin-bottom: 10px;
                /* position: static; <- This was original, changed in 1024px query for sticky */
            }

            .color-panel,
            .settings-panel,
            .storage-panel {
                display: flex;
                margin-bottom: 0;
                flex-wrap: wrap;
                justify-content: center;
            }

            .color-option {
                margin: 3px;
            }

            .completion-toggle {
                margin: 3px;
            }

            .right-panel {
                max-width: 100%;
                width: 100%;
                margin-left: 0;
                margin-right: 0;
            }

            .calendar-grid {
                gap: 1px;
            }

            .calendar-day {
                min-height: 30px;
                padding: 3px 1px;
                font-size: 11px;
            }

            .hour-row {
                height: auto;
                min-height: 40px;
            }

            .task-input {
                min-height: 40px;
            }

            .action-btn {
                min-height: 40px;
            }

            .hour-label {
                min-width: 40px;
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <div class="left-panel">
            <div class="color-panel">
                <div class="color-option selected" style="background-color: #ffcccc;" data-color="#ffcccc"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ccffcc;" data-color="#ccffcc"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ccccff;" data-color="#ccccff"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ffffcc;" data-color="#ffffcc"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ffccff;" data-color="#ffccff"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ccffff;" data-color="#ccffff"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #8d8d8d;" data-color="#8d8d8d"> <span
                        class="color-option-title"></span> </div>
                <div class="color-option" style="background-color: #ffffff;" data-color="#ffffff"> <span
                        class="color-option-title"></span> </div>
            </div>
            <div class="settings-panel">
                <button id="settings-btn" class="settings-btn" data-i18n-key="settings">设置</button>
            </div>

            <div class="storage-panel">
                <button id="storage-btn" class="settings-btn" data-i18n-key="storageBasket">收纳筐</button>
            </div>

            <div class="completion-toggle" id="completion-toggle" title="标记当日完成"></div>
        </div>

        <div class="main-content">
            <h1 data-i18n-key="mainTitle">24小时时间管理</h1>
            <div class="app-info" data-i18n-key="appInfo">点击或拖动选择时间段，右侧输入备注</div>

            <!-- REMOVED date-selector and date-nav -->
            <!-- Hidden input to store current date, controlled by calendar -->
            <input type="hidden" id="current-date-value-holder">

            <div class="controls">
                <button id="clear-all" style="background-color: #f44336; color: white;"
                    data-i18n-key="clearAll">清空</button>
                <button id="save-template" style="background-color: #2196F3; color: white;"
                    data-i18n-key="saveTemplate">保存为模板</button>
                <button id="load-template" style="background-color: #ff9800; color: white;"
                    data-i18n-key="loadTemplate">加载模板</button>
            </div>

            <div id="time-container"></div>

            <div class="notes-panel" id="notes-panel">
                <div class="notes-header">
                    <div class="notes-title" data-i18n-key="notesTitle">文本块</div>
                    <div class="notes-controls">
                        <button id="toggle-notes-collapse" data-i18n-key="collapse">收缩</button>
                        <select class="notes-select" id="notes-select">
                            <option value="0">1</option>
                            <option value="1">2</option>
                            <option value="2">3</option>
                            <option value="3">4</option>
                            <option value="4">5</option>
                        </select>
                    </div>
                </div>
                <div class="notes-content" id="notes-content" contenteditable="true"></div>
            </div>

            <button class="save-btn hidden" id="save-btn" data-i18n-key="savePlan">保存计划</button>
        </div>

        <div class="right-panel" id="calendar-panel">
            <div class="calendar-panel-header">
                <span id="calendar-panel-title" data-i18n-key="calendarTitle">日历面板</span> <!-- Add i18n key -->
                <button id="toggle-calendar-collapse" data-i18n-key="collapseCalendar">收起</button> <!-- Add i18n key -->
            </div>
            <div class="calendar-header">
                <button id="prev-calendar-month">←</button>
                <h3 id="current-calendar-month-year"></h3>
                <button id="next-calendar-month">→</button>
            </div>
            <div class="calendar-grid">
                <div class="calendar-day-header">日</div>
                <div class="calendar-day-header">一</div>
                <div class="calendar-day-header">二</div>
                <div class="calendar-day-header">三</div>
                <div class="calendar-day-header">四</div>
                <div class="calendar-day-header">五</div>
                <div class="calendar-day-header">六</div>
            </div>
            <div class="score-display">
                <span data-i18n-key="totalScore">当前总积分：</span>
                <span id="total-score">0</span>
            </div>
        </div>
    </div>

    <!-- Modals remain the same -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="settings-section">
                <h4 data-i18n-key="sleepSettings">睡眠时间设置</h4>
                <div class="time-selector">
                    <div> <label for="sleep-start" data-i18n-key="sleepStart">开始时间</label> <select id="sleep-start">
                            <option value="22">22:00</option>
                            <option value="23" selected="">23:00</option>
                            <option value="0">00:00</option>
                            <option value="1">01:00</option>
                            <option value="2">02:00</option>
                            <option value="3">03:00</option>
                            <option value="4">04:00</option>
                        </select> </div>
                    <div> <label for="sleep-end" data-i18n-key="sleepEnd">结束时间</label> <select id="sleep-end">
                            <option value="5">05:00</option>
                            <option value="6">06:00</option>
                            <option value="7" selected="">07:00</option>
                            <option value="8">08:00</option>
                            <option value="9">09:00</option>
                            <option value="10">10:00</option>
                            <option value="11">11:00</option>
                        </select> </div>
                </div>
                <div class="modal-buttons"> <button class="confirm-btn" id="confirm-sleep"
                        data-i18n-key="confirm">确定</button> <button class="cancel-btn" id="remove-sleep"
                        style="background-color: #9d4e49;" data-i18n-key="noSleep">不设置睡眠</button> </div>
            </div>
            <div class="settings-section">
                <h4 data-i18n-key="colorSettings">颜色设置</h4>
                <div class="color-edit-container">
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ffcccc;"></div> <input type="color"
                            class="color-edit-input" value="#ffcccc" data-index="0"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="0">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ccffcc;"></div> <input type="color"
                            class="color-edit-input" value="#ccffcc" data-index="1"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="1">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ccccff;"></div> <input type="color"
                            class="color-edit-input" value="#ccccff" data-index="2"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="2">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ffffcc;"></div> <input type="color"
                            class="color-edit-input" value="#ffffcc" data-index="3"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="3">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ffccff;"></div> <input type="color"
                            class="color-edit-input" value="#ffccff" data-index="4"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="4">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ccffff;"></div> <input type="color"
                            class="color-edit-input" value="#ccffff" data-index="5"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="5">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #8d8d8d;"></div> <input type="color"
                            class="color-edit-input" value="#8d8d8d" data-index="6"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="6">
                    </div>
                    <div class="color-edit-item">
                        <div class="color-edit-preview" style="background-color: #ffffff;"></div> <input type="color"
                            class="color-edit-input" value="#ffffff" data-index="7"> <input type="text"
                            class="color-edit-input" value="" placeholder="颜色名称" data-index="7">
                    </div>
                </div>
                <div class="modal-buttons"> <button class="confirm-btn" id="save-colors"
                        data-i18n-key="saveColors">保存颜色设置</button> <button class="confirm-btn" id="reset-colors"
                        style="background-color: #9d4e49;" data-i18n-key="resetColors">恢复默认</button> </div>
            </div>
            <div class="settings-section">
                <h4 data-i18n-key="displaySettings">显示设置</h4>
                <div class="settings-option"> <label for="auto-save" data-i18n-key="autoSave">自动保存</label> <label
                        class="switch"> <input type="checkbox" id="auto-save"> <span class="slider"></span> </label>
                </div>
                <div class="settings-option"> <label for="invert-current"
                        data-i18n-key="invertCurrent">当前时间块文字反色</label> <label class="switch"> <input type="checkbox"
                            id="invert-current"> <span class="slider"></span> </label> </div>
                <div class="settings-option"> <label for="enable-notes" data-i18n-key="enableNotes">启用文本块</label> <label
                        class="switch"> <input type="checkbox" id="enable-notes"> <span class="slider"></span> </label>
                </div>
            </div>
            <div class="modal-buttons"> <button class="cancel-btn" id="close-settings" data-i18n-key="close">关闭</button>
            </div>
        </div>
    </div>
    <div class="modal" id="storage-modal">
        <div class="modal-content">
            <h3 class="modal-title" data-i18n-key="storageTitle">收纳筐</h3>
            <div id="storage-items-container">
                <div class="storage-category" data-i18n-key="followup">跟进中</div>
                <div id="followup-items"></div>
                <div class="storage-category" data-i18n-key="todo">待办事项</div>
                <div id="todo-items"></div>
            </div>
            <div class="modal-buttons" style="margin-top:15px;"> <button class="confirm-btn" id="clear-storage"
                    data-i18n-key="clearStorage">清空收纳筐</button> <button class="cancel-btn" id="close-storage"
                    data-i18n-key="close">关闭</button> </div>
        </div>
    </div>
    <div class="modal" id="template-modal">
        <div class="modal-content">
            <h3 class="modal-title" data-i18n-key="templateManage">模板管理</h3>
            <div id="template-items-container">
                <div id="template-items"></div>
            </div>
            <div class="modal-buttons" style="margin-top:15px;"> <button class="confirm-btn" id="confirm-template"
                    data-i18n-key="confirm">确定</button> <button class="cancel-btn" id="cancel-template"
                    data-i18n-key="cancel">取消</button> </div>
        </div>
    </div>
    <div class="modal" id="edit-modal">
        <div class="modal-content">
            <h3 class="modal-title" data-i18n-key="editTask">编辑任务</h3>
            <div style="margin-bottom: 15px;"> <label for="edit-hour" data-i18n-key="editTime">时间 (0-23):</label> <input
                    type="range" id="edit-hour" min="0" max="23" style="width: 100%;"> <span
                    id="edit-hour-value">0</span> </div>
            <div style="margin-bottom: 15px;"> <label for="edit-task" data-i18n-key="editTaskContent">任务内容:</label>
                <input type="text" id="edit-task" style="width: 100%; padding: 5px;"> </div>
            <div style="margin-bottom: 15px;"> <label data-i18n-key="color">颜色:</label>
                <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                    <div class="color-option edit-color-option selected" style="background-color: #ffcccc;"
                        data-color="#ffcccc"></div>
                    <div class="color-option edit-color-option" style="background-color: #ccffcc;" data-color="#ccffcc">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #ccccff;" data-color="#ccccff">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #ffffcc;" data-color="#ffffcc">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #ffccff;" data-color="#ffccff">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #ccffff;" data-color="#ccffff">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #8d8d8d;" data-color="#8d8d8d">
                    </div>
                    <div class="color-option edit-color-option" style="background-color: #ffffff;" data-color="#ffffff">
                    </div>
                </div>
            </div>
            <div class="category-switch"> <button class="category-followup" id="set-followup"
                    data-i18n-key="convertFollowup">转换为跟进</button> <button class="category-todo" id="set-todo"
                    data-i18n-key="convertTodo">转换为待办事项</button> </div>
            <div class="modal-buttons" style="margin-top:15px;"> <button class="confirm-btn" id="confirm-edit"
                    data-i18n-key="save">保存</button> <button class="cancel-btn" id="cancel-edit"
                    data-i18n-key="cancel">取消</button> </div>
        </div>
    </div>


    <script>
        // 多语言翻译字典 (仅中文)
        const translations = {
            title: "24小时时间管理",
            mainTitle: "24小时时间管理",
            appInfo: "点击或拖动选择时间段，右侧输入备注",
            clearAll: "清空",
            saveTemplate: "保存为模板",
            loadTemplate: "加载模板",
            savePlan: "保存计划",
            work: "工作",
            study: "学习",
            rest: "休息",
            sport: "运动",
            entertainment: "娱乐",
            social: "社交",
            others: "其他",
            unassigned: "未安排",
            settings: "设置",
            storageBasket: "收纳筐",
            notesTitle: "文本块",
            collapse: "收缩", // For notes
            expand: "展开",   // For notes
            sleepSettings: "睡眠时间设置",
            sleepStart: "开始时间",
            sleepEnd: "结束时间",
            confirm: "确定",
            noSleep: "不设置睡眠",
            colorSettings: "颜色设置",
            saveColors: "保存颜色设置",
            resetColors: "恢复默认",
            displaySettings: "显示设置",
            autoSave: "自动保存",
            invertCurrent: "当前时间块文字反色",
            enableNotes: "启用文本块",
            close: "关闭",
            storageTitle: "收纳筐",
            followup: "跟进中",
            todo: "待办事项",
            clearStorage: "清空收纳筐",
            templateManage: "模板管理",
            cancel: "取消",
            editTask: "编辑任务",
            editTime: "时间 (0-23):",
            editTaskContent: "任务内容:",
            color: "颜色:",
            convertFollowup: "转换为跟进",
            convertTodo: "转换为待办事项",
            save: "保存",
            clear: "清空",
            moveUp: "整体上移",
            moveDown: "整体下移",
            actionBtn: "操作",
            totalScore: "当前总积分：",
            calendarTitle: "日历面板", // New
            collapseCalendar: "收起", // New
            expandCalendar: "展开"    // New
        };

        let currentLang = 'zh';

        function updateLanguage() {
            document.querySelectorAll('[data-i18n-key]').forEach(el => {
                const key = el.getAttribute('data-i18n-key');
                if (translations[key]) {
                    if (el.classList.contains('color-option-title')) {
                        el.textContent = translations[key];
                    } else if (el.tagName === 'INPUT' && el.type === 'submit' || el.tagName === 'BUTTON' || el.tagName === 'TITLE') {
                        el.innerText = translations[key];
                        if (el.tagName === 'TITLE') document.title = translations[key];
                    } else if (el.placeholder && el.tagName === 'INPUT') {
                        el.placeholder = translations[key];
                    }
                    else {
                        el.innerText = translations[key];
                    }
                }
            });
            if (typeof renderCalendar === "function" && currentCalendarDate) {
                const year = currentCalendarDate.getFullYear();
                const month = currentCalendarDate.getMonth();
                const currentMonthYearEl = document.getElementById('current-calendar-month-year');
                if (currentMonthYearEl) currentMonthYearEl.textContent = `${year}年 ${month + 1}月`;
            }
        }


        document.addEventListener('DOMContentLoaded', function () {
            function getLocalDateString(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // Use a hidden input to store the "current-date" for the planner
            // The visible date input is removed. Calendar clicks will update this hidden input.
            const plannerDateHolder = document.getElementById('current-date-value-holder');
            plannerDateHolder.value = getLocalDateString(new Date()); // Initialize to today

            let selectedColor = '#ffcccc';
            let isDragging = false;
            let sleepTime = JSON.parse(localStorage.getItem('sleep-time')) || null;
            let storage = JSON.parse(localStorage.getItem('time-storage')) || [];
            let activeActionMenu = null;
            let lastSleepEnd = -1;
            let currentTodos = {};
            let templates = JSON.parse(localStorage.getItem('time-templates')) || {};
            let currentEditingItem = null;
            let completionStatus = JSON.parse(localStorage.getItem('completion-status')) || {};

            let settings = JSON.parse(localStorage.getItem('app-settings')) || {
                autoSave: false,
                invertCurrent: false,
                enableNotes: false,
                colorOptions: [
                    { color: '#ffcccc', title: '' }, { color: '#ccffcc', title: '' },
                    { color: '#ccccff', title: '' }, { color: '#ffffcc', title: '' },
                    { color: '#ffccff', title: '' }, { color: '#ccffff', title: '' },
                    { color: '#8d8d8d', title: '' }, { color: '#ffffff', title: '' }
                ],
                calendarCollapsed: false // New setting
            };

            let notes = JSON.parse(localStorage.getItem('time-notes')) || {
                pages: ['', '', '', '', ''],
                currentPage: 0
            };

            let currentCalendarDate = new Date(plannerDateHolder.value); // Sync calendar with planner's initial date
            const calendarGridEl = document.querySelector('.calendar-grid');
            const currentMonthYearEl = document.getElementById('current-calendar-month-year');
            const totalScoreEl = document.getElementById('total-score');
            const calendarPanelEl = document.getElementById('calendar-panel');
            const toggleCalendarCollapseBtn = document.getElementById('toggle-calendar-collapse');
            const dayHeadersCount = 7;

            // --- Function to get current planner date ---
            function getCurrentPlannerDateString() {
                return plannerDateHolder.value;
            }

            function initColorOptions() { /* ... (same as before) ... */ }
            function saveColorSettings() { /* ... (same as before) ... */ }

            // Modified autoSave to use getCurrentPlannerDateString
            function autoSave() {
                if (!settings.autoSave) return;
                const date = getCurrentPlannerDateString(); // Use helper
                // Save time blocks
                const timeBlocks = [];
                document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => {
                    const hour = parseInt(row.dataset.hour);
                    const colorBlock = row.querySelector('.color-block');
                    const taskInput = row.querySelector('.task-input');
                    const completeCheckbox = row.querySelector('.complete-checkbox input');
                    timeBlocks.push({ hour: hour, color: colorBlock.dataset.color || '#ffffff', task: taskInput.value, completed: completeCheckbox.checked });
                });
                const data = { date: date, timeBlocks: timeBlocks };
                localStorage.setItem(`time-blocks-${date}`, JSON.stringify(data));

                // Also save notes if enabled
                if (settings.enableNotes) {
                    saveNotes();
                }
            }
            function setupAutoSaveListeners() { /* ... (same as before) ... */ }
            function updateSaveButton() { /* ... (same as before) ... */ }
            const container = document.getElementById('time-container');

            // Modified generateTimeBlocks to use getCurrentPlannerDateString
            function generateTimeBlocks() {
                saveCurrentTodos();
                container.innerHTML = '';
                lastSleepEnd = -1;
                const currentPlannerDate = getCurrentPlannerDateString(); // Get current planner date

                for (let hour = 0; hour < 24; hour++) {
                    // ... (hour row generation logic remains largely the same) ...
                    if (sleepTime && hour === sleepTime.end && lastSleepEnd !== sleepTime.end) {
                        const divider = document.createElement('div');
                        divider.className = 'sleep-divider';
                        container.appendChild(divider);
                        lastSleepEnd = sleepTime.end;
                    }
                    const hourRow = document.createElement('div');
                    hourRow.className = 'hour-row';
                    hourRow.dataset.hour = hour;
                    if (sleepTime && isSleepTime(hour)) {
                        hourRow.classList.add('hidden-row');
                        continue;
                    }
                    const hourLabel = document.createElement('div');
                    hourLabel.className = 'hour-label';
                    hourLabel.textContent = `${hour}:00`;
                    const currentHourVal = new Date().getHours();
                    // Compare currentPlannerDate with today's date string for highlighting
                    if (hour === currentHourVal && currentPlannerDate === getLocalDateString(new Date()) && settings.invertCurrent) {
                        hourLabel.classList.add('current-hour');
                    }
                    // ... (rest of elements: colorBlock, taskInput, taskActions, actionMenu) ...
                    const colorBlock = document.createElement('div');
                    colorBlock.className = 'color-block';
                    colorBlock.dataset.color = '#ffffff';
                    colorBlock.style.backgroundColor = '#ffffff';
                    const taskInput = document.createElement('input');
                    taskInput.type = 'text';
                    taskInput.className = 'task-input';
                    const taskActions = document.createElement('div');
                    taskActions.className = 'task-actions';
                    const completeCheckboxDiv = document.createElement('div');
                    completeCheckboxDiv.className = 'complete-checkbox';
                    const checkboxInput = document.createElement('input');
                    checkboxInput.type = 'checkbox';
                    completeCheckboxDiv.appendChild(checkboxInput);
                    const actionBtn = document.createElement('div');
                    actionBtn.className = 'action-btn';
                    actionBtn.textContent = translations.actionBtn;
                    const actionMenu = document.createElement('div');
                    actionMenu.className = 'action-menu';
                    actionMenu.innerHTML = `
                    <button class="cut-btn" data-i18n-key="clear">${translations.clear}</button>
                    <button class="move-up-btn" data-i18n-key="moveUp">${translations.moveUp}</button>
                    <button class="move-down-btn" data-i18n-key="moveDown">${translations.moveDown}</button>
                    <button class="followup-btn" data-i18n-key="followup">${translations.followup}</button>
                    <button class="todo-btn" data-i18n-key="todo">${translations.todo}</button>
                `;
                    taskActions.appendChild(completeCheckboxDiv);
                    taskActions.appendChild(actionBtn);
                    hourRow.appendChild(hourLabel);
                    hourRow.appendChild(colorBlock);
                    hourRow.appendChild(taskInput);
                    hourRow.appendChild(taskActions);
                    hourRow.appendChild(actionMenu);
                    container.appendChild(hourRow);

                    // Use currentPlannerDate for todoKey
                    const todoKey = `todo-${currentPlannerDate}-${hour}`;
                    if (currentTodos[todoKey]) {
                        const todo = currentTodos[todoKey];
                        colorBlock.style.backgroundColor = todo.color;
                        colorBlock.dataset.color = todo.color;
                        taskInput.value = todo.task;
                        checkboxInput.checked = todo.completed;
                        hourLabel.classList.toggle('completed', todo.completed);
                        colorBlock.classList.toggle('completed', todo.completed);
                        taskInput.classList.toggle('completed', todo.completed);
                    }
                    // ... (event listeners for hourRow elements) ...
                    colorBlock.addEventListener('click', function () { this.style.backgroundColor = selectedColor; this.dataset.color = selectedColor; if (settings.autoSave) autoSave(); });
                    colorBlock.addEventListener('mousedown', function (e) { e.preventDefault(); isDragging = true; this.style.backgroundColor = selectedColor; this.dataset.color = selectedColor; if (settings.autoSave) autoSave(); });
                    colorBlock.addEventListener('mouseover', function () { if (isDragging) { this.style.backgroundColor = selectedColor; this.dataset.color = selectedColor; if (settings.autoSave) autoSave(); } });
                    checkboxInput.addEventListener('change', function () { const isCompleted = this.checked; hourLabel.classList.toggle('completed', isCompleted); colorBlock.classList.toggle('completed', isCompleted); taskInput.classList.toggle('completed', isCompleted); if (settings.autoSave) autoSave(); });
                    taskInput.addEventListener('blur', function () { if (settings.autoSave) autoSave(); });
                    actionBtn.addEventListener('click', function (e) { e.stopPropagation(); if (activeActionMenu && activeActionMenu !== actionMenu) { activeActionMenu.style.display = 'none'; } actionMenu.style.display = actionMenu.style.display === 'block' ? 'none' : 'block'; activeActionMenu = actionMenu.style.display === 'block' ? actionMenu : null; });
                    actionMenu.querySelector('.cut-btn').addEventListener('click', function () { colorBlock.style.backgroundColor = '#ffffff'; colorBlock.dataset.color = '#ffffff'; taskInput.value = ''; checkboxInput.checked = false; hourLabel.classList.remove('completed'); colorBlock.classList.remove('completed'); taskInput.classList.remove('completed'); actionMenu.style.display = 'none'; activeActionMenu = null; if (settings.autoSave) autoSave(); });
                    actionMenu.querySelector('.move-up-btn').addEventListener('click', function (e) { e.stopPropagation(); moveUp(hourRow); actionMenu.style.display = 'none'; activeActionMenu = null; if (settings.autoSave) autoSave(); });
                    actionMenu.querySelector('.move-down-btn').addEventListener('click', function (e) { e.stopPropagation(); moveDown(hourRow); actionMenu.style.display = 'none'; activeActionMenu = null; if (settings.autoSave) autoSave(); });
                    actionMenu.querySelector('.followup-btn').addEventListener('click', function () { addToStorage(hourRow, 'followup', false); });
                    actionMenu.querySelector('.todo-btn').addEventListener('click', function () { addToStorage(hourRow, 'todo', true); });
                }
                document.removeEventListener('click', handleGlobalClickForActionMenu);
                document.addEventListener('click', handleGlobalClickForActionMenu);
                setupAutoSaveListeners();
                loadDateData(currentPlannerDate); // Load data for the current planner date
                updateCompletionToggle();
            }

            function handleGlobalClickForActionMenu() { /* ... (same as before) ... */ }
            function moveUp(row) { /* ... (same as before) ... */ }
            function moveDown(row) { /* ... (same as before) ... */ }

            function updateCompletionToggle() {
                const plannerDate = getCurrentPlannerDateString();
                const toggle = document.getElementById('completion-toggle');
                toggle.classList.toggle('completed', completionStatus[plannerDate] || false);
            }

            // Modified saveCurrentTodos to use getCurrentPlannerDateString
            function saveCurrentTodos() {
                const currentPlannerDate = getCurrentPlannerDateString();
                currentTodos = {};
                document.querySelectorAll('#time-container .hour-row').forEach(row => {
                    if (row.classList.contains('hidden-row')) return;
                    const hour = row.dataset.hour;
                    const colorBlock = row.querySelector('.color-block');
                    const taskInput = row.querySelector('.task-input');
                    const completeCheckbox = row.querySelector('.complete-checkbox input');
                    if (colorBlock && taskInput && completeCheckbox) {
                        const todoKey = `todo-${currentPlannerDate}-${hour}`; // Use plannerDate
                        currentTodos[todoKey] = { color: colorBlock.dataset.color || '#ffffff', task: taskInput.value, completed: completeCheckbox.checked };
                    }
                });
            }
            function addToStorage(row, category, clearItem) { /* ... (same as before) ... */ }
            function isSleepTime(hour) { /* ... (same as before) ... */ }
            function loadStorageItems() { /* ... (same as before) ... */ }
            function editStorageItem(index) { /* ... (same as before) ... */ }
            function saveEditedItem() { /* ... (same as before) ... */ }
            function setItemFollowup() { /* ... (same as before) ... */ }
            function setItemTodo() { /* ... (same as before) ... */ }
            function saveAndCloseEditModal() { /* ... (same as before) ... */ }
            function applyStorageItem(item) { /* ... (same as before) ... */ }

            // Modified loadDateData to use getCurrentPlannerDateString
            function loadDateData(dateToLoad) { // Parameter is the date string
                document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => {
                    // ... (clearing logic is the same) ...
                    const hourLabel = row.querySelector('.hour-label'); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input');
                    colorBlock.style.backgroundColor = '#ffffff'; colorBlock.dataset.color = '#ffffff'; taskInput.value = ''; completeCheckbox.checked = false;
                    hourLabel.classList.remove('completed'); colorBlock.classList.remove('completed'); taskInput.classList.remove('completed');
                });
                const savedData = localStorage.getItem(`time-blocks-${dateToLoad}`); // Use passed date
                if (savedData) {
                    const data = JSON.parse(savedData);
                    data.timeBlocks.forEach(block => {
                        const row = document.querySelector(`#time-container .hour-row[data-hour="${block.hour}"]`);
                        if (row && !row.classList.contains('hidden-row')) {
                            // ... (applying data logic is the same) ...
                            const hourLabel = row.querySelector('.hour-label'); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input');
                            colorBlock.style.backgroundColor = block.color; colorBlock.dataset.color = block.color; taskInput.value = block.task || ''; completeCheckbox.checked = block.completed || false;
                            hourLabel.classList.toggle('completed', block.completed); colorBlock.classList.toggle('completed', block.completed); taskInput.classList.toggle('completed', block.completed);
                        }
                    });
                }
            }
            function saveAsTemplate() { /* ... (same as before) ... */ }
            function loadTemplate() { /* ... (same as before) ... */ }
            function applyTemplate(templateName) { /* ... (same as before) ... */ }

            // The main planner date change is now driven by calendar clicks,
            // so the prev/next day buttons on main planner are removed.
            // changeDate function is no longer directly needed for UI buttons but used by calendar click.
            function changePlannerDate(newDateString) {
                saveCurrentTodos();
                plannerDateHolder.value = newDateString;
                currentTodos = {};
                generateTimeBlocks(); // This will call loadDateData for the new date
                renderCalendar(); // Update calendar to highlight new planner date
            }

            function toggleCompletionStatus() {
                const plannerDate = getCurrentPlannerDateString();
                completionStatus[plannerDate] = !(completionStatus[plannerDate] || false);
                localStorage.setItem('completion-status', JSON.stringify(completionStatus));
                updateCompletionToggle();
                renderCalendar();
                updateTotalScore();
            }

            function loadSettings() {
                // Load settings from localStorage and apply them
                const autoSaveEl = document.getElementById('auto-save');
                const invertCurrentEl = document.getElementById('invert-current');
                const enableNotesEl = document.getElementById('enable-notes');
                const sleepStartEl = document.getElementById('sleep-start');
                const sleepEndEl = document.getElementById('sleep-end');

                if (autoSaveEl) autoSaveEl.checked = settings.autoSave;
                if (invertCurrentEl) invertCurrentEl.checked = settings.invertCurrent;
                if (enableNotesEl) enableNotesEl.checked = settings.enableNotes;

                updateSaveButton();
                toggleNotesPanel();

                // Apply calendar collapse state
                if (settings.calendarCollapsed) {
                    calendarPanelEl.classList.add('collapsed');
                    toggleCalendarCollapseBtn.textContent = translations.expandCalendar;
                } else {
                    calendarPanelEl.classList.remove('collapsed');
                    toggleCalendarCollapseBtn.textContent = translations.collapseCalendar;
                }

                // Apply sleep time settings
                if (sleepTime) {
                    if (sleepStartEl) sleepStartEl.value = sleepTime.start;
                    if (sleepEndEl) sleepEndEl.value = sleepTime.end;
                }
            }
            function saveSettings() {
                /* ... same as before ... */
                settings.autoSave = document.getElementById('auto-save').checked;
                settings.invertCurrent = document.getElementById('invert-current').checked;
                settings.enableNotes = document.getElementById('enable-notes').checked;
                // settings.calendarCollapsed is saved by its own toggle function
                localStorage.setItem('app-settings', JSON.stringify(settings));
                updateSaveButton();
                toggleNotesPanel();
                generateTimeBlocks();
            }
            function toggleNotesPanel() { /* ... (same as before) ... */ }
            function loadNotes() { /* ... (same as before) ... */ }
            function saveNotes() { /* ... (same as before) ... */ }
            function changeNotePage() { /* ... (same as before) ... */ }

            // --- Calendar Panel Functions ---
            function renderCalendar() {
                const year = currentCalendarDate.getFullYear();
                const month = currentCalendarDate.getMonth();

                currentMonthYearEl.textContent = `${year}年 ${month + 1}月`;

                while (calendarGridEl.children.length > dayHeadersCount) {
                    calendarGridEl.removeChild(calendarGridEl.lastChild);
                }

                const firstDayOfMonth = new Date(year, month, 1);
                const daysInMonth = new Date(year, month + 1, 0).getDate();
                const startingDay = firstDayOfMonth.getDay();

                const todayDateString = getLocalDateString(new Date());
                const plannerDateString = getCurrentPlannerDateString(); // Use helper

                for (let i = 0; i < startingDay; i++) {
                    const emptyCell = document.createElement('div');
                    emptyCell.classList.add('calendar-day', 'empty');
                    calendarGridEl.appendChild(emptyCell);
                }

                for (let day = 1; day <= daysInMonth; day++) {
                    const dayCell = document.createElement('div');
                    dayCell.classList.add('calendar-day');

                    const dayNumberSpan = document.createElement('span');
                    dayNumberSpan.classList.add('day-number');
                    dayNumberSpan.textContent = day;
                    dayCell.appendChild(dayNumberSpan);

                    const cellDate = new Date(year, month, day);
                    const cellDateString = getLocalDateString(cellDate);

                    if (cellDateString === todayDateString) {
                        dayCell.classList.add('today');
                    }
                    if (cellDateString === plannerDateString) {
                        dayCell.classList.add('selected-planner-date');
                    }

                    if (completionStatus[cellDateString]) {
                        const scoreMarker = document.createElement('span');
                        scoreMarker.classList.add('day-score-marker');
                        scoreMarker.textContent = '+1分';
                        dayCell.appendChild(scoreMarker);
                    }

                    dayCell.addEventListener('click', () => {
                        const clickedDateString = getLocalDateString(new Date(year, month, day));
                        changePlannerDate(clickedDateString); // Use the new helper
                    });
                    calendarGridEl.appendChild(dayCell);
                }
            }

            function updateTotalScore() {
                let score = 0;
                for (const date in completionStatus) {
                    if (completionStatus[date]) {
                        score++;
                    }
                }
                totalScoreEl.textContent = score;
            }

            function toggleCalendarPanel() {
                calendarPanelEl.classList.toggle('collapsed');
                settings.calendarCollapsed = calendarPanelEl.classList.contains('collapsed');
                localStorage.setItem('app-settings', JSON.stringify(settings)); // Save state
                if (settings.calendarCollapsed) {
                    toggleCalendarCollapseBtn.textContent = translations.expandCalendar;
                } else {
                    toggleCalendarCollapseBtn.textContent = translations.collapseCalendar;
                }
            }

            // --- Initial Setup Calls ---
            loadSettings(); // Try to load settings first (will handle missing DOM elements gracefully)
            initColorOptions();
            updateLanguage();
            // generateTimeBlocks must be called *after* plannerDateHolder is set
            // and *after* currentCalendarDate is synced with it.
            // currentCalendarDate is already synced.
            generateTimeBlocks();
            loadNotes();
            renderCalendar();
            updateTotalScore();

            // Force initialization by simulating settings button click and close
            // This ensures all settings are properly loaded and applied
            setTimeout(() => {
                const settingsBtn = document.getElementById('settings-btn');
                const closeSettingsBtn = document.getElementById('close-settings');
                if (settingsBtn && closeSettingsBtn) {
                    settingsBtn.click(); // Open settings to initialize
                    setTimeout(() => {
                        closeSettingsBtn.click(); // Close settings to apply
                    }, 50);
                }
            }, 100);

            // --- Event Listeners Setup ---
            toggleCalendarCollapseBtn.addEventListener('click', toggleCalendarPanel);

            document.getElementById('prev-calendar-month').addEventListener('click', () => {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
                renderCalendar();
            });

            document.getElementById('next-calendar-month').addEventListener('click', () => {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
                renderCalendar();
            });

            // --- PASTE ALL PREVIOUS JS LOGIC HERE, THEN THE MODIFIED/NEW FUNCTIONS ABOVE ---
            initColorOptions = function () {
                const colorOptionsSettings = settings.colorOptions || [{ color: '#ffcccc', title: '' }, { color: '#ccffcc', title: '' }, { color: '#ccccff', title: '' }, { color: '#ffffcc', title: '' }, { color: '#ffccff', title: '' }, { color: '#ccffff', title: '' }, { color: '#8d8d8d', title: '' }, { color: '#ffffff', title: '' }];
                const colorOptionsElements = document.querySelectorAll('.color-panel .color-option');
                colorOptionsElements.forEach((option, index) => {
                    if (colorOptionsSettings[index]) {
                        option.style.backgroundColor = colorOptionsSettings[index].color;
                        option.dataset.color = colorOptionsSettings[index].color;
                        const titleSpan = option.querySelector('.color-option-title');
                        if (titleSpan) {
                            // Use custom title from settings, not translations
                            titleSpan.textContent = colorOptionsSettings[index].title || '';
                        }
                    }
                });
                const editColorOptions = document.querySelectorAll('.edit-color-option');
                editColorOptions.forEach((option, index) => { if (colorOptionsSettings[index]) { option.style.backgroundColor = colorOptionsSettings[index].color; option.dataset.color = colorOptionsSettings[index].color; } });
                const colorEditInputs = document.querySelectorAll('.color-edit-input');
                colorOptionsSettings.forEach((option, index) => { const colorPicker = document.querySelector(`.color-edit-input[type="color"][data-index="${index}"]`); const textInput = document.querySelector(`.color-edit-input[type="text"][data-index="${index}"]`); const preview = document.querySelectorAll('.color-edit-preview')[index]; if (colorPicker) colorPicker.value = option.color; if (textInput) textInput.value = option.title; if (preview) preview.style.backgroundColor = option.color; });
                const firstMainColorOption = document.querySelector('.color-panel .color-option');
                if (firstMainColorOption) { document.querySelectorAll('.color-panel .color-option').forEach(opt => opt.classList.remove('selected')); firstMainColorOption.classList.add('selected'); selectedColor = firstMainColorOption.dataset.color; }
            };
            saveColorSettings = function () {
                const newColorOptions = []; const colorEditItems = document.querySelectorAll('.color-edit-item');
                colorEditItems.forEach((item, index) => { const colorPicker = item.querySelector('input[type="color"]'); const textInput = item.querySelector('input[type="text"]'); newColorOptions.push({ color: colorPicker.value, title: textInput.value || '' }); });
                settings.colorOptions = newColorOptions; localStorage.setItem('app-settings', JSON.stringify(settings)); initColorOptions(); updateLanguage(); alert(translations.saveColors);
            };
            setupAutoSaveListeners = function () {
                document.querySelectorAll('#time-container .task-input').forEach(input => { input.removeEventListener('blur', autoSave); input.addEventListener('blur', autoSave); });
                document.querySelectorAll('#time-container .complete-checkbox input').forEach(checkbox => { checkbox.removeEventListener('change', autoSave); checkbox.addEventListener('change', autoSave); });
            };
            updateSaveButton = function () {
                const saveBtn = document.getElementById('save-btn'); if (settings.autoSave) { saveBtn.classList.add('hidden'); } else { saveBtn.classList.remove('hidden'); }
            };
            handleGlobalClickForActionMenu = function () { if (activeActionMenu) { activeActionMenu.style.display = 'none'; activeActionMenu = null; } };
            moveUp = function (row) {
                let currentHour = parseInt(row.dataset.hour); function getRowState(r) { return { color: r.querySelector('.color-block').dataset.color || '#ffffff', task: r.querySelector('.task-input').value, completed: r.querySelector('.complete-checkbox input').checked }; } function setRowState(r, state) { let colorBlock = r.querySelector('.color-block'); let taskInput = r.querySelector('.task-input'); let checkbox = r.querySelector('.complete-checkbox input'); colorBlock.style.backgroundColor = state.color; colorBlock.dataset.color = state.color; taskInput.value = state.task; checkbox.checked = state.completed; let hourLabel = r.querySelector('.hour-label'); hourLabel.classList.toggle('completed', state.completed); colorBlock.classList.toggle('completed', state.completed); taskInput.classList.toggle('completed', state.completed); } function isRowEmpty(r) { if (!r) return true; let color = r.querySelector('.color-block').dataset.color; let task = r.querySelector('.task-input').value.trim(); return (color === '#ffffff' || color === '') && task === '' && !r.querySelector('.complete-checkbox input').checked; }
                let blockToMove = []; let startHourOfBlock = currentHour; for (let h = currentHour; h >= 0; h--) { let r = document.querySelector(`#time-container .hour-row[data-hour="${h}"]`); if (r && !isRowEmpty(r)) { startHourOfBlock = h; } else { break; } } for (let h = startHourOfBlock; h <= currentHour; h++) { let r = document.querySelector(`#time-container .hour-row[data-hour="${h}"]`); if (r && !isRowEmpty(r)) { blockToMove.push(r); } } if (blockToMove.length === 0) return;
                let firstRowInBlockHour = parseInt(blockToMove[0].dataset.hour); let targetHourForFirstRow = firstRowInBlockHour - 1; if (targetHourForFirstRow < 0) return; let targetRowForFirst = document.querySelector(`#time-container .hour-row[data-hour="${targetHourForFirstRow}"]`); if (!targetRowForFirst || (targetRowForFirst && !isRowEmpty(targetRowForFirst))) { if (targetRowForFirst && targetRowForFirst.classList.contains('hidden-row')) { alert("无法移动到睡眠时间段"); return; } if (targetRowForFirst && !isRowEmpty(targetRowForFirst)) { alert("上方时间段非空，无法移动"); return; } }
                for (let r of blockToMove) { let originalHour = parseInt(r.dataset.hour); let newHour = originalHour - 1; let prevRow = document.querySelector(`#time-container .hour-row[data-hour="${newHour}"]`); if (prevRow) { let state = getRowState(r); setRowState(prevRow, state); } } let lastRowInOriginalBlock = blockToMove[blockToMove.length - 1]; setRowState(lastRowInOriginalBlock, { color: '#ffffff', task: '', completed: false });
            };
            moveDown = function (row) {
                let currentHour = parseInt(row.dataset.hour); function getRowState(r) { return { color: r.querySelector('.color-block').dataset.color || '#ffffff', task: r.querySelector('.task-input').value, completed: r.querySelector('.complete-checkbox input').checked }; } function setRowState(r, state) { let cb = r.querySelector('.color-block'), ti = r.querySelector('.task-input'), chk = r.querySelector('.complete-checkbox input'), hl = r.querySelector('.hour-label'); cb.style.backgroundColor = state.color; cb.dataset.color = state.color; ti.value = state.task; chk.checked = state.completed; hl.classList.toggle('completed', state.completed); cb.classList.toggle('completed', state.completed); ti.classList.toggle('completed', state.completed); } function isRowEmpty(r) { if (!r) return true; let c = r.querySelector('.color-block').dataset.color; let t = r.querySelector('.task-input').value.trim(); return (c === '#ffffff' || c === '') && t === '' && !r.querySelector('.complete-checkbox input').checked; }
                let blockToMove = []; let endHourOfBlock = currentHour; for (let h = currentHour; h < 24; h++) { let r = document.querySelector(`#time-container .hour-row[data-hour="${h}"]`); if (r && !isRowEmpty(r)) { endHourOfBlock = h; } else { break; } } for (let h = currentHour; h <= endHourOfBlock; h++) { let r = document.querySelector(`#time-container .hour-row[data-hour="${h}"]`); if (r) blockToMove.push(r); } if (blockToMove.length === 0) return;
                let lastRowInBlockHour = parseInt(blockToMove[blockToMove.length - 1].dataset.hour); let targetHourForLastRow = lastRowInBlockHour + 1; if (targetHourForLastRow > 23) return; let targetRowForLast = document.querySelector(`#time-container .hour-row[data-hour="${targetHourForLastRow}"]`); if (!targetRowForLast || (targetRowForLast && !isRowEmpty(targetRowForLast))) { if (targetRowForLast && targetRowForLast.classList.contains('hidden-row')) { alert("无法移动到睡眠时间段"); return; } if (targetRowForLast && !isRowEmpty(targetRowForLast)) { alert("下方时间段非空，无法移动"); return; } }
                for (let i = blockToMove.length - 1; i >= 0; i--) { let r = blockToMove[i]; let originalHour = parseInt(r.dataset.hour); let newHour = originalHour + 1; let nextRow = document.querySelector(`#time-container .hour-row[data-hour="${newHour}"]`); if (nextRow) { let state = getRowState(r); setRowState(nextRow, state); } } let firstRowInOriginalBlock = blockToMove[0]; setRowState(firstRowInOriginalBlock, { color: '#ffffff', task: '', completed: false });
            };
            addToStorage = function (row, category, clearItem) {
                const hour = row.dataset.hour; const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input'); if (taskInput.value.trim() === '') { alert(translations.appInfo); return; }
                storage.push({ hour: hour, color: colorBlock.dataset.color || '#ffffff', task: taskInput.value, completed: completeCheckbox.checked, date: new Date().toLocaleString('zh-CN'), category: category }); localStorage.setItem('time-storage', JSON.stringify(storage));
                if (clearItem) { colorBlock.style.backgroundColor = '#ffffff'; colorBlock.dataset.color = '#ffffff'; taskInput.value = ''; completeCheckbox.checked = false; row.querySelector('.hour-label').classList.remove('completed'); colorBlock.classList.remove('completed'); taskInput.classList.remove('completed'); }
                row.querySelector('.action-menu').style.display = 'none'; activeActionMenu = null; const categoryText = category === 'followup' ? translations.followup : translations.todo; alert(`已添加到 ${categoryText}`); if (settings.autoSave) autoSave();
            };
            isSleepTime = function (hour) { if (!sleepTime) return false; const h = parseInt(hour); const start = parseInt(sleepTime.start); const end = parseInt(sleepTime.end); if (start <= end) { return h >= start && h < end; } else { return h >= start || h < end; } };
            loadStorageItems = function () {
                const followupContainer = document.getElementById('followup-items'); const todoContainer = document.getElementById('todo-items'); followupContainer.innerHTML = ''; todoContainer.innerHTML = ''; const followupItemsFragment = document.createDocumentFragment(); const todoItemsFragment = document.createDocumentFragment(); let hasFollowup = false; let hasTodo = false;
                storage.forEach((item, index) => { const containerFragment = item.category === 'followup' ? followupItemsFragment : todoItemsFragment; if (item.category === 'followup') hasFollowup = true; if (item.category === 'todo') hasTodo = true; const storageItem = document.createElement('div'); storageItem.className = 'storage-item'; storageItem.innerHTML = ` <div class="storage-item-completed"> <input type="checkbox" ${item.completed ? 'checked' : ''} data-index="${index}"> </div> <div class="storage-item-time">${item.hour}:00 - ${item.task}</div> <div class="storage-item-task">创建于: ${item.date}</div> <div class="storage-item-actions"> <button class="edit-item" data-index="${index}">${translations.editTask}</button> <button class="delete-item" data-index="${index}">${translations.clear}</button> </div> `; storageItem.querySelector('.storage-item-completed input').addEventListener('change', function () { storage[index].completed = this.checked; localStorage.setItem('time-storage', JSON.stringify(storage)); }); storageItem.addEventListener('click', function (e) { if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'INPUT' && !e.target.closest('.storage-item-actions')) { if (confirm(`是否将此项应用到 ${item.hour}:00 时间段？`)) { applyStorageItem(item); document.getElementById('storage-modal').style.display = 'none'; } } }); storageItem.querySelector('.edit-item').addEventListener('click', function (e) { e.stopPropagation(); editStorageItem(index); }); storageItem.querySelector('.delete-item').addEventListener('click', function (e) { e.stopPropagation(); if (confirm('确定要删除此项吗？')) { storage.splice(index, 1); localStorage.setItem('time-storage', JSON.stringify(storage)); loadStorageItems(); } }); containerFragment.appendChild(storageItem); });
                followupContainer.appendChild(followupItemsFragment); todoContainer.appendChild(todoItemsFragment);
                if (!hasFollowup) { followupContainer.innerHTML = `<p style="text-align:center;color:#999;padding:10px;">${translations.followup}为空</p>`; } if (!hasTodo) { todoContainer.innerHTML = `<p style="text-align:center;color:#999;padding:10px;">${translations.todo}为空</p>`; }
                if (storage.length === 0 && (!hasFollowup && !hasTodo)) { const storageItemsContainer = document.getElementById('storage-items-container'); followupContainer.innerHTML = ''; todoContainer.innerHTML = ''; if (!storageItemsContainer.querySelector('.empty-storage-message')) { const emptyMessage = document.createElement('p'); emptyMessage.className = 'empty-storage-message'; emptyMessage.style.textAlign = 'center'; emptyMessage.style.color = '#999'; emptyMessage.style.padding = '10px'; emptyMessage.textContent = translations.storageBasket + '为空'; const todoCat = storageItemsContainer.querySelector('#todo-items'); if (todoCat) { todoCat.insertAdjacentElement('afterend', emptyMessage); } else { storageItemsContainer.appendChild(emptyMessage); } } } else { const existingEmptyMsg = document.getElementById('storage-items-container').querySelector('.empty-storage-message'); if (existingEmptyMsg) existingEmptyMsg.remove(); }
            };
            editStorageItem = function (index) {
                const item = storage[index]; if (!item) return; currentEditingItem = index; document.getElementById('edit-hour').value = item.hour; document.getElementById('edit-hour-value').innerText = item.hour; document.getElementById('edit-task').value = item.task;
                document.querySelectorAll('.edit-color-option').forEach(option => { option.classList.remove('selected'); if (option.dataset.color === item.color) { option.classList.add('selected'); } }); if (!document.querySelector('.edit-color-option.selected')) { document.querySelector('.edit-color-option[data-color="#ffffff"]').classList.add('selected'); }
                const setFollowupBtn = document.getElementById('set-followup'); const setTodoBtn = document.getElementById('set-todo'); setFollowupBtn.disabled = item.category === 'followup'; setTodoBtn.disabled = item.category === 'todo'; document.getElementById('edit-modal').style.display = 'flex';
            };
            saveEditedItem = function () {
                if (currentEditingItem === null || !storage[currentEditingItem]) return; const hour = parseInt(document.getElementById('edit-hour').value); const task = document.getElementById('edit-task').value.trim(); const selectedColorOption = document.querySelector('.edit-color-option.selected');
                if (isNaN(hour) || hour < 0 || hour > 23) { alert('请输入有效的时间 (0-23)'); return; } if (!task) { alert('请输入任务内容'); return; } if (!selectedColorOption) { alert('请选择颜色'); return; }
                const color = selectedColorOption.dataset.color; storage[currentEditingItem] = { ...storage[currentEditingItem], hour: hour, task: task, color: color }; localStorage.setItem('time-storage', JSON.stringify(storage)); loadStorageItems(); document.getElementById('edit-modal').style.display = 'none'; currentEditingItem = null;
            };
            setItemFollowup = function () { if (currentEditingItem === null || !storage[currentEditingItem]) return; storage[currentEditingItem].category = 'followup'; saveAndCloseEditModal(); };
            setItemTodo = function () { if (currentEditingItem === null || !storage[currentEditingItem]) return; storage[currentEditingItem].category = 'todo'; saveAndCloseEditModal(); };
            saveAndCloseEditModal = function () {
                const taskValue = document.getElementById('edit-task').value.trim(); if (taskValue) { const hour = parseInt(document.getElementById('edit-hour').value); const selectedColorOption = document.querySelector('.edit-color-option.selected'); const color = selectedColorOption ? selectedColorOption.dataset.color : '#ffffff'; storage[currentEditingItem].hour = hour; storage[currentEditingItem].task = taskValue; storage[currentEditingItem].color = color; }
                localStorage.setItem('time-storage', JSON.stringify(storage)); loadStorageItems(); document.getElementById('edit-modal').style.display = 'none'; currentEditingItem = null;
            };
            applyStorageItem = function (item) {
                const row = document.querySelector(`#time-container .hour-row[data-hour="${item.hour}"]`); if (row && !row.classList.contains('hidden-row')) { const hourLabel = row.querySelector('.hour-label'); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input'); colorBlock.style.backgroundColor = item.color; colorBlock.dataset.color = item.color; taskInput.value = item.task; completeCheckbox.checked = item.completed; hourLabel.classList.toggle('completed', item.completed); colorBlock.classList.toggle('completed', item.completed); taskInput.classList.toggle('completed', item.completed); if (settings.autoSave) autoSave(); } else { alert(`时间段 ${item.hour}:00 当前不可用 (可能在睡眠时间或未显示).`); }
            };
            saveAsTemplate = function () {
                const templateName = prompt('请输入模板名称:'); if (!templateName || templateName.trim() === "") { if (templateName !== null) alert("模板名称不能为空"); return; } const timeBlocks = []; document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => { const hour = parseInt(row.dataset.hour); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input'); timeBlocks.push({ hour: hour, color: colorBlock.dataset.color || '#ffffff', task: taskInput.value, completed: completeCheckbox.checked }); });
                templates[templateName.trim()] = timeBlocks; localStorage.setItem('time-templates', JSON.stringify(templates)); alert(`模板 "${templateName.trim()}" 保存成功！`);
            };
            loadTemplate = function () {
                const templateModal = document.getElementById('template-modal'); const templateItems = document.getElementById('template-items'); templateItems.innerHTML = ''; if (Object.keys(templates).length === 0) { templateItems.innerHTML = `<p style="text-align:center;color:#999;padding:10px;">${translations.templateManage}为空</p>`; } else { Object.keys(templates).forEach(templateName => { const templateItemDiv = document.createElement('div'); templateItemDiv.className = 'storage-item'; templateItemDiv.innerHTML = ` <div class="storage-item-time">${templateName}</div> <div class="storage-item-actions"> <button class="delete-item" data-name="${templateName}">${translations.clear}</button> </div> `; templateItemDiv.addEventListener('click', function (e) { if (e.target.tagName !== 'BUTTON' && !e.target.closest('.storage-item-actions')) { if (confirm(`是否加载模板 "${templateName}"？`)) { applyTemplate(templateName); templateModal.style.display = 'none'; } } }); templateItemDiv.querySelector('.delete-item').addEventListener('click', function (e) { e.stopPropagation(); if (confirm(`确定要删除模板 "${templateName}"吗？`)) { delete templates[templateName]; localStorage.setItem('time-templates', JSON.stringify(templates)); loadTemplate(); } }); templateItems.appendChild(templateItemDiv); }); }
                templateModal.style.display = 'flex';
            };
            applyTemplate = function (templateName) {
                const timeBlocks = templates[templateName]; if (!timeBlocks) return; document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => { const hourLabel = row.querySelector('.hour-label'); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input'); colorBlock.style.backgroundColor = '#ffffff'; colorBlock.dataset.color = '#ffffff'; taskInput.value = ''; completeCheckbox.checked = false; hourLabel.classList.remove('completed'); colorBlock.classList.remove('completed'); taskInput.classList.remove('completed'); });
                timeBlocks.forEach(block => { const row = document.querySelector(`#time-container .hour-row[data-hour="${block.hour}"]`); if (row && !row.classList.contains('hidden-row')) { const hourLabel = row.querySelector('.hour-label'); const colorBlock = row.querySelector('.color-block'); const taskInput = row.querySelector('.task-input'); const completeCheckbox = row.querySelector('.complete-checkbox input'); colorBlock.style.backgroundColor = block.color; colorBlock.dataset.color = block.color; taskInput.value = block.task; completeCheckbox.checked = block.completed; hourLabel.classList.toggle('completed', block.completed); colorBlock.classList.toggle('completed', block.completed); taskInput.classList.toggle('completed', block.completed); } }); if (settings.autoSave) autoSave();
            };
            toggleNotesPanel = function () { const notesPanel = document.getElementById('notes-panel'); notesPanel.style.display = settings.enableNotes ? 'block' : 'none'; };
            loadNotes = function () { document.getElementById('notes-content').innerHTML = notes.pages[notes.currentPage] || ''; document.getElementById('notes-select').value = notes.currentPage; };
            saveNotes = function () {
                const content = document.getElementById('notes-content').innerHTML;
                notes.pages[notes.currentPage] = content;
                localStorage.setItem('time-notes', JSON.stringify(notes));
                console.log('Notes saved for page', notes.currentPage, ':', content.substring(0, 50) + '...');
            };
            changeNotePage = function () { saveNotes(); notes.currentPage = parseInt(document.getElementById('notes-select').value); loadNotes(); };

            document.getElementById('toggle-notes-collapse').addEventListener('click', function () { const notesContent = document.getElementById('notes-content'); const isCollapsed = notesContent.style.display === 'none'; notesContent.style.display = isCollapsed ? 'block' : 'none'; this.innerText = isCollapsed ? translations.collapse : translations.expand; });
            document.getElementById('edit-hour').addEventListener('input', function () { document.getElementById('edit-hour-value').innerText = this.value; });
            document.addEventListener('mouseup', function () { isDragging = false; });
            document.querySelectorAll('.color-panel .color-option').forEach(option => { option.addEventListener('click', function () { document.querySelectorAll('.color-panel .color-option').forEach(opt => opt.classList.remove('selected')); this.classList.add('selected'); selectedColor = this.dataset.color; }); });
            document.querySelectorAll('.edit-color-option').forEach(option => { option.addEventListener('click', function () { document.querySelectorAll('.edit-color-option').forEach(opt => opt.classList.remove('selected')); this.classList.add('selected'); }); });
            document.getElementById('save-btn').addEventListener('click', function () { const date = getCurrentPlannerDateString(); const timeBlocks = []; document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => { timeBlocks.push({ hour: parseInt(row.dataset.hour), color: row.querySelector('.color-block').dataset.color || '#ffffff', task: row.querySelector('.task-input').value, completed: row.querySelector('.complete-checkbox input').checked }); }); localStorage.setItem(`time-blocks-${date}`, JSON.stringify({ date: date, timeBlocks: timeBlocks })); alert(translations.savePlan + '成功！'); });
            document.getElementById('clear-all').addEventListener('click', function () { if (confirm(translations.clearAll + '所有颜色和文字？')) { document.querySelectorAll('#time-container .hour-row:not(.hidden-row)').forEach(row => { const hl = row.querySelector('.hour-label'), cb = row.querySelector('.color-block'), ti = row.querySelector('.task-input'), chk = row.querySelector('.complete-checkbox input'); cb.style.backgroundColor = '#ffffff'; cb.dataset.color = '#ffffff'; ti.value = ''; chk.checked = false; hl.classList.remove('completed'); cb.classList.remove('completed'); ti.classList.remove('completed'); }); if (settings.autoSave) autoSave(); } });
            document.getElementById('save-template').addEventListener('click', saveAsTemplate);
            document.getElementById('load-template').addEventListener('click', loadTemplate);
            document.getElementById('cancel-template').addEventListener('click', () => document.getElementById('template-modal').style.display = 'none');
            document.getElementById('confirm-template').addEventListener('click', () => { document.getElementById('template-modal').style.display = 'none'; });
            // Removed prev/next day buttons for main planner
            const settingsModal = document.getElementById('settings-modal');
            document.getElementById('settings-btn').addEventListener('click', function () { document.getElementById('sleep-start').value = sleepTime ? sleepTime.start : 23; document.getElementById('sleep-end').value = sleepTime ? sleepTime.end : 7; initColorOptions(); settingsModal.style.display = 'flex'; });
            document.getElementById('close-settings').addEventListener('click', function () { saveSettings(); settingsModal.style.display = 'none'; });
            document.getElementById('confirm-sleep').addEventListener('click', function () { const start = parseInt(document.getElementById('sleep-start').value); const end = parseInt(document.getElementById('sleep-end').value); if (start === end) { alert('开始时间和结束时间不能相同'); return; } sleepTime = { start: start, end: end }; localStorage.setItem('sleep-time', JSON.stringify(sleepTime)); saveCurrentTodos(); generateTimeBlocks(); saveSettings(); settingsModal.style.display = 'none'; });
            document.getElementById('remove-sleep').addEventListener('click', function () { sleepTime = null; localStorage.removeItem('sleep-time'); saveCurrentTodos(); generateTimeBlocks(); saveSettings(); settingsModal.style.display = 'none'; });
            document.getElementById('save-colors').addEventListener('click', saveColorSettings);
            document.getElementById('reset-colors').addEventListener('click', function () { settings.colorOptions = [{ color: '#ffcccc', title: '' }, { color: '#ccffcc', title: '' }, { color: '#ccccff', title: '' }, { color: '#ffffcc', title: '' }, { color: '#ffccff', title: '' }, { color: '#ccffff', title: '' }, { color: '#8d8d8d', title: '' }, { color: '#ffffff', title: '' }]; localStorage.setItem('app-settings', JSON.stringify(settings)); initColorOptions(); updateLanguage(); alert(translations.resetColors + '成功'); });
            settingsModal.addEventListener('click', e => { if (e.target === settingsModal) { saveSettings(); settingsModal.style.display = 'none'; } });
            document.querySelectorAll('.color-edit-input[type="color"]').forEach(input => { input.addEventListener('input', function () { document.querySelectorAll('.color-edit-preview')[parseInt(this.dataset.index)].style.backgroundColor = this.value; }); });
            const storageModal = document.getElementById('storage-modal');
            document.getElementById('storage-btn').addEventListener('click', () => { loadStorageItems(); storageModal.style.display = 'flex'; });
            document.getElementById('clear-storage').addEventListener('click', () => { if (confirm(translations.clearStorage + '?')) { storage = []; localStorage.setItem('time-storage', JSON.stringify(storage)); loadStorageItems(); } });
            document.getElementById('close-storage').addEventListener('click', () => storageModal.style.display = 'none');
            storageModal.addEventListener('click', e => { if (e.target === storageModal) storageModal.style.display = 'none'; });
            const editModal = document.getElementById('edit-modal');
            document.getElementById('confirm-edit').addEventListener('click', saveEditedItem);
            document.getElementById('cancel-edit').addEventListener('click', () => { editModal.style.display = 'none'; currentEditingItem = null; });
            document.getElementById('set-followup').addEventListener('click', setItemFollowup);
            document.getElementById('set-todo').addEventListener('click', setItemTodo);
            editModal.addEventListener('click', e => { if (e.target === editModal) { editModal.style.display = 'none'; currentEditingItem = null; } });
            document.getElementById('completion-toggle').addEventListener('click', toggleCompletionStatus);
            document.getElementById('auto-save').addEventListener('change', function () { settings.autoSave = this.checked; localStorage.setItem('app-settings', JSON.stringify(settings)); updateSaveButton(); console.log('Auto-save setting changed to:', settings.autoSave); });
            document.getElementById('invert-current').addEventListener('change', function () { settings.invertCurrent = this.checked; localStorage.setItem('app-settings', JSON.stringify(settings)); }); // Save immediately
            document.getElementById('enable-notes').addEventListener('change', function () { settings.enableNotes = this.checked; localStorage.setItem('app-settings', JSON.stringify(settings)); toggleNotesPanel(); }); // Save immediately
            document.getElementById('notes-select').addEventListener('change', changeNotePage);
            document.getElementById('notes-content').addEventListener('blur', saveNotes);

            // Add auto-save for notes content
            document.getElementById('notes-content').addEventListener('input', function() {
                console.log('Notes content changed, auto-save enabled:', settings.autoSave);
                if (settings.autoSave) {
                    // Debounce the save to avoid too frequent saves
                    clearTimeout(this.saveTimeout);
                    this.saveTimeout = setTimeout(() => {
                        console.log('Auto-saving notes content...');
                        saveNotes();
                    }, 1000); // Save after 1 second of no typing
                }
            });

        });
    </script>
</body>

</html>